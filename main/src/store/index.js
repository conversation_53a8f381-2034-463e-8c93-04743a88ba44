import { createStore } from 'vuex'
import router from '@/router'
import api from '@/api'
import io from 'socket.io-client'
import { qiankunActions } from '@/qiankun'
import inactivityDetector from 'common/js/inactivityDetector'
import { loginRole, globalReadConfig, globalReadOpenConfig, rongCloudToken } from 'common/js/GlobalMethod'
import * as RongIMLib from '@rongcloud/imlib-next'
import customizeStore from 'customize/config/store'
import FilePreviewConfirm from '@/components/file-preview-confirm'
import { ElMessage } from 'element-plus'

const stateData = {
  theme: {},
  user: {},
  menu: [],
  area: [],
  role: [],
  readConfig: {},
  readOpenConfig: {},
  layoutElement: '',
  workBenchElement: '',
  versionUpdate: false,
  socket: null,
  // 实现子应用直接跳转子应用
  openRoute: { name: '', path: '', query: {} },
  // 实现子应用关闭当前页面跳转子应用
  closeOpenRoute: { openId: '', closeId: '' },
  // 导入文件
  importList: [],
  // 下载附件
  downloadFile: [],
  // 批量下载附件
  batchDownloadFile: [],
  // 扩展下载附件
  extendDownloadFile: [],
  // 导出word
  exportWordObj: {},
  // 导出word带html的
  exportWordHtmlObj: {},
  // 批量导出word
  exportWordList: {},
  // 批量导出word带html的
  exportWordHtmlList: {},
  // 操作任务管理器
  globalCentralControlObj: {},
  // 消息盒子刷新
  boxMessageRefresh: false,
  // 待办刷新
  personalDoRefresh: false,
  // 融云请求地址
  isPrivatization: false,
  // 融云请求地址
  rongCloudUrl: '',
  // 融云appkey
  rongCloudKey: '',
  // 融云用户token
  rongCloudToken: '',
  AiChatCode: '',
  AiChatElShow: true,
  AiChatWidth: 400,
  AiChatConfig: {},
  AiChatWindow: '',
  AiChatFile: [],
  AiChatParams: {},
  AiChatContent: '',
  AiChatSetContent: '',
  AiChatAddContent: '',
  AiChatSendMessage: '',
  AiChatToolSendMessage: {}
}
// 获取第一个斜杠和第二个斜杠之间的内容
// const routePath = (url) => {
//   let path = '' // 第二个斜杠前内容
//   const first = url.indexOf('/') + 1 // 从第一个斜杠算起（+1表示不包括该斜杠）
//   const kong = url.indexOf(' ', first) // 第一个斜杠后的第一个空格
//   const heng = url.indexOf('/', first) // 第一个斜杠后的第一个斜杠（即第二个斜杠）
//   if (heng === -1) {
//     path = url.substring(1, kong)
//   } else {
//     path = url.substring(1, heng)
//   }
//   return path
// }
// const pathMenu = (menuList) => {
//   var pathList = ['system', 'interaction']
//   menuList.forEach(v => {
//     if (v.routePath) {
//       const name = routePath(v.routePath)
//       if (name !== '/') {
//         pathList = [...new Set([...pathList, name])]
//       }
//     }
//     if (v.children?.length) {
//       pathList = [...new Set([...pathList, ...pathMenu(v.children)])]
//     }
//   })
//   return pathList
// }
// 首次进来默认选中
const handleMethods = (data, id) => {
  let areaItem = {}
  let areaTotal = data.length
  for (let index = 0; index < data.length; index++) {
    const item = data[index]
    if (item.id === id) areaItem = item
    if (item.children && item.children.length) {
      const areaData = handleMethods(item.children, id)
      areaTotal += areaData?.areaTotal
      if (areaData?.areaItem.id === id) areaItem = areaData?.areaItem
    }
  }
  return { areaItem, areaTotal }
}
const parseKeyValueString = (str) => {
  return str
    .split(',')
    .map((pair) => pair.split('=').map((item) => item.trim()))
    .reduce((obj, [key, value]) => {
      obj[key] = value
      return obj
    }, {})
}
export default createStore({
  state: stateData,
  getters: {
    getThemeFn(state) {
      return state.theme
    },
    getUserFn(state) {
      return state.user || JSON.parse(sessionStorage.getItem('user'))
    },
    getMenuFn(state) {
      return state.menu || JSON.parse(sessionStorage.getItem('menu'))
    },
    getAreaFn(state) {
      return state.area || JSON.parse(sessionStorage.getItem('area'))
    },
    getRoleFn(state) {
      return state.role || JSON.parse(sessionStorage.getItem('role'))
    },
    getReadConfig(state) {
      return state.readConfig
    },
    getReadOpenConfig(state) {
      return state.readOpenConfig
    },
    getLayoutElement(state) {
      return state.layoutElement
    },
    getWorkBenchElement(state) {
      return state.workBenchElement
    },
    getIsPrivatization(state) {
      return state.isPrivatization
    },
    getRongCloudUrl(state) {
      return state.rongCloudUrl
    },
    getRongCloudKey(state) {
      return state.rongCloudKey
    },
    getRongCloudToken(state) {
      return state.rongCloudToken
    }
  },
  mutations: {
    setState(state) {
      for (const key in stateData) {
        if (Object.prototype.hasOwnProperty.call(stateData, key)) {
          state[key] = stateData[key]
        }
      }
      // 停止监听
      inactivityDetector.stop()
    },
    setTheme(state, theme = {}) {
      state.theme = theme
      qiankunActions.setGlobalState({ theme })
    },
    setUser(state, user = {}) {
      state.user = user
      qiankunActions.setGlobalState({ user })
    },
    setMenu(state, menu = []) {
      state.menu = menu
      qiankunActions.setGlobalState({ menu })
    },
    setArea(state, area = []) {
      state.area = area
      qiankunActions.setGlobalState({ area })
    },
    setRole(state, role = []) {
      state.role = role
      qiankunActions.setGlobalState({ role })
    },
    setReadConfig(state, readConfig = {}) {
      state.readConfig = readConfig
      qiankunActions.setGlobalState({ readConfig })
      if (readConfig.systemInactivityTimeout) {
        if (readConfig.systemInactivityTimeout === 'false') {
          // 停止监听
          inactivityDetector.stop()
        } else {
          // 重新设置超时时间（比如改为1分钟）并重置计时器
          const minute = Number(readConfig.systemInactivityTimeout) || 5
          if (minute) inactivityDetector.resetTimeout(minute * 60 * 1000)
        }
      } else {
        // 停止监听
        inactivityDetector.stop()
      }
    },
    setReadOpenConfig(state, readOpenConfig = {}) {
      state.readOpenConfig = readOpenConfig
      qiankunActions.setGlobalState({ readOpenConfig })
    },
    setLayoutElement(state, layoutElement) {
      state.layoutElement = layoutElement
    },
    setWorkBenchElement(state, workBenchElement) {
      state.workBenchElement = workBenchElement
    },
    setOpenRoute(state, openRoute = { name: '', path: '', query: {} }) {
      state.openRoute = openRoute
    },
    setCloseOpenRoute(state, closeOpenRoute = { openId: '', closeId: '' }) {
      state.closeOpenRoute = closeOpenRoute
    },
    setVersionUpdate(state, versionUpdate = false) {
      state.versionUpdate = versionUpdate
    },
    setImportList(state, importList = []) {
      state.importList = importList
    },
    setDownloadFile(state, downloadFile = []) {
      state.downloadFile = downloadFile
    },
    setBatchDownloadFile(state, batchDownloadFile = []) {
      state.batchDownloadFile = batchDownloadFile
    },
    setExtendDownloadFile(state, extendDownloadFile = []) {
      state.extendDownloadFile = extendDownloadFile
    },
    setExportWordObj(state, exportWordObj = []) {
      state.exportWordObj = exportWordObj
    },
    setExportWordHtmlObj(state, exportWordHtmlObj = []) {
      state.exportWordHtmlObj = exportWordHtmlObj
    },
    setExportWordList(state, exportWordList = []) {
      state.exportWordList = exportWordList
    },
    setExportWordHtmlList(state, exportWordHtmlList = []) {
      state.exportWordHtmlList = exportWordHtmlList
    },
    setGlobalCentralControlObj(state, globalCentralControlObj = []) {
      state.globalCentralControlObj = globalCentralControlObj
    },
    setBoxMessageRefresh(state, boxMessageRefresh = []) {
      state.boxMessageRefresh = boxMessageRefresh
    },
    setPersonalDoRefresh(state, personalDoRefresh = []) {
      state.personalDoRefresh = personalDoRefresh
    },
    setRongCloudToken(state, rongCloudToken = []) {
      state.rongCloudToken = rongCloudToken
    },
    setAiChatCode(state, AiChatCode = '') {
      state.AiChatCode = AiChatCode
      qiankunActions.setGlobalState({ AiChatCode })
    },
    setAiChatElShow(state, AiChatElShow = false) {
      state.AiChatElShow = AiChatElShow
      qiankunActions.setGlobalState({ AiChatElShow })
    },
    setAiChatWidth(state, AiChatWidth = 400) {
      state.AiChatWidth = AiChatWidth
      qiankunActions.setGlobalState({ AiChatWidth })
    },
    setAiChatConfig(state, AiChatConfig = {}) {
      state.AiChatConfig = AiChatConfig
      qiankunActions.setGlobalState({ AiChatConfig })
    },
    setAiChatWindow(state, AiChatWindow = false) {
      state.AiChatWindow = AiChatWindow
      qiankunActions.setGlobalState({ AiChatWindow })
    },
    setAiChatFile(state, AiChatFile = {}) {
      state.AiChatFile = AiChatFile
      qiankunActions.setGlobalState({ AiChatFile })
    },
    setAiChatParams(state, AiChatParams = {}) {
      state.AiChatParams = AiChatParams
      qiankunActions.setGlobalState({ AiChatParams })
    },
    setAiChatContent(state, AiChatContent = '') {
      state.AiChatContent = AiChatContent
      qiankunActions.setGlobalState({ AiChatContent })
    },
    setAiChatSetContent(state, AiChatSetContent = '') {
      state.AiChatSetContent = AiChatSetContent
      qiankunActions.setGlobalState({ AiChatSetContent })
    },
    setAiChatAddContent(state, AiChatAddContent = '') {
      state.AiChatAddContent = AiChatAddContent
      qiankunActions.setGlobalState({ AiChatAddContent })
    },
    setAiChatSendMessage(state, AiChatSendMessage = '') {
      state.AiChatSendMessage = AiChatSendMessage
      qiankunActions.setGlobalState({ AiChatSendMessage })
    },
    setAiChatToolSendMessage(state, AiChatToolSendMessage = {}) {
      state.AiChatToolSendMessage = AiChatToolSendMessage
      qiankunActions.setGlobalState({ AiChatToolSendMessage })
    }
  },
  actions: {
    async loginUser({ commit, dispatch }, type) {
      const { data: user, extData } = await api.loginUser()
      const { data: area } = await api.loginAreas()
      const getUser = sessionStorage.getItem('user') || '{}'
      if (getUser === '{}' && extData) user.areaId = extData
      const img = user.photo || user.headImg
      user.image = img ? api.fileURL(img) : api.defaultImgURL('default_user_head.jpg')
      sessionStorage.setItem('user', JSON.stringify(user))
      sessionStorage.setItem('AreaId', user.areaId)
      sessionStorage.setItem('area', JSON.stringify(area))
      const { areaItem, areaTotal } = handleMethods(area, user.areaId)
      user.areaTotal = areaTotal
      commit('setUser', user)
      commit('setArea', area)
      if (areaItem.remarks) {
        const areaConfig = parseKeyValueString(areaItem.remarks)
        commit('setLayoutElement', areaConfig?.Layout)
        commit('setWorkBenchElement', areaConfig?.WorkBench)
      } else {
        commit('setLayoutElement', '')
        commit('setWorkBenchElement', '')
      }
      dispatch('loginMenu')
      // loginArea()
      loginRole()
      globalReadConfig()
      globalReadOpenConfig()
      dispatch('globalRongCloudConfig')
      // dispatch('globalSocketMethod')
      // const isPath = router.options.routes.filter(v => !['LoginView', 'LayoutContainer'].includes(v.name)).map(v => v.path)
      // const path = router.currentRoute.value.path
      if (type) {
        if (['login'].includes(type)) {
          router.push({ path: '/' })
        } else {
          router.push({ path: type })
        }
      }
      // 开始全局监听
      inactivityDetector.start()
      // 设置不活跃回调
      inactivityDetector.setInactiveCallback(() => {
        dispatch('loginOut', '为保障您的账户安全，系统检测到您长时间未操作。为防范风险，当前会话自动登出。')
      })
    },
    async loginMenu({ commit }) {
      const { data: menu } = await api.loginMenu()
      if (!menu?.length && !['FilePrevieOpen'].includes(router?.currentRoute?.value?.name))
        router.push({ path: '/NotFoundPage' })
      sessionStorage.setItem('menu', JSON.stringify(menu))
      commit('setMenu', menu)
      // prefetchMicroMenu(pathMenu(menu))
    },
    async loginOut({ commit }, message) {
      const { code } = await api.loginOut()
      if (code === 200) {
        sessionStorage.clear()
        const goal_login_router_path = localStorage.getItem('goal_login_router_path')
        if (goal_login_router_path) {
          const goal_login_router_query = localStorage.getItem('goal_login_router_query') || ''
          router.push({
            path: goal_login_router_path,
            query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {}
          })
        } else {
          router.push({ path: '/LoginView' })
        }
        commit('setState')
        globalReadOpenConfig()
        ElMessage({ message: message, showClose: true, type: 'success' })
      }
    },
    async globalRongCloudConfig({ state }) {
      const { data } = await api.globalReadConfig({
        codes: [
          'isInitRongCloud',
          'isPrivatization',
          'appRongKey',
          'appRongNavigators',
          'appRongAddress',
          'appTomcatAddress',
          'appOnlyHeader',
          'appOnlyHeader'
        ]
      })
      const isPrivatization = data.isPrivatization === 'true'
      state.isPrivatization = isPrivatization
      state.rongCloudUrl = data.appRongAddress || data.appTomcatAddress
      if (!state.rongCloudKey) {
        state.rongCloudKey = data.appRongKey
        if (isPrivatization && data.appRongKey && data.appRongNavigators) {
          RongIMLib.init({
            appkey: data.appRongKey,
            navigators: data.appRongNavigators.split(','),
            environment: 'intranet'
          })
        } else {
          RongIMLib.init({ appkey: data.appRongKey || 'y745wfm84be5v' })
        }
      }
      if (data.isInitRongCloud === 'true' && (data.appRongAddress || data.appTomcatAddress) && !state.rongCloudToken) {
        rongCloudToken(
          isPrivatization,
          data.appRongKey || 'y745wfm84be5v',
          data.appRongAddress || data.appTomcatAddress,
          data.appOnlyHeader
        )
      }
    },
    handleFilePreview(store, file) {
      if (file) FilePreviewConfirm({ query: file })
    },
    globalSocketMethod({ state }) {
      const token = sessionStorage.getItem('token') || ''
      const socket = io('http://**************:54390/push', {
        path: '/push',
        'force new connection': true,
        reconnection: true,
        reconnectionDelay: 2000,
        reconnectionDelayMax: 5000,
        reconnectionAttempts: 10,
        timeout: 10000,
        transports: ['websocket', 'polling'],
        query: { Authorization: token, uri: window.location.pathname }
      })
      state.socket = socket
    }
  },
  modules: {
    customizeStore
  }
})
