/**
 * tinymce 工具函数
 * 无状态函数、可移植
 */
import api from '@/api'
import { icon } from './icon'
import { initTpLayout } from './tpLayout'

export const uuid = (prefix) => `${prefix}_${Date.now() + Math.floor(Math.random() * 1000000)}`

export const getTinymce = () => {
  const root = typeof window !== 'undefined' ? window : global
  return root && 'tinymce' in root ? root.tinymce : null
}
export const getContent = (editor) => editor ? editor.getContent() : ''

export const setContent = (val, editor) => editor ? editor.setContent(val) : ''

export function resetContent (val, editor) {
  if (!editor) return
  if (editor.resetContent) return editor.resetContent(val)
  // 若无 reset fun，则手动 reset
  editor.setContent(val)
  editor.setDirty(false) // 恢复初始状态
  editor.undoManager.clear()
}

export function setModeDisabled (editor, disabled = true) {
  if (!editor) return
  editor.mode.set(disabled ? 'readonly' : 'design')
}

export const imageUploadHandler = (blobInfo, success, failure) => {
  // if (blobInfo.blob().size / 1024 / 1024 > 2) {
  //   failure('上传失败，图片大小请控制在 2M 以内', { remove: true })
  //   return
  // } else {
  const option = { file: new File([blobInfo.blob()], blobInfo.filename()) }
  const params = new FormData()
  params.append('file', option.file)
  params.append('uuid', uuid('file'))
  params.append('isKeepAlive', true)
  api.globalUpload(params, () => { }).then(res => {
    success(api.openImgURL(res.data.newFileName))
  }).catch(() => {
    failure('上传出错，服务器开小差了呢')
  })
  // }
}

export function getContentStyle (style) {
  const defaultStyle = 'body{font-size: 12pt;font-family: 微软雅黑;} img{padding: 0 2px;} p{margin: 0;}'
  return defaultStyle + (style ? style : '')
}

export const initCustomButton = (editor, customButton) => {
  if (!editor.ui.registry.getAll().icons['importWord']) { editor.ui.registry.addIcon('importWord', icon.importWord) }
  if (!editor.ui.registry.getAll().icons['textRectify']) { editor.ui.registry.addIcon('textRectify', icon.textRectify) }
  if (!editor.ui.registry.getAll().icons['tpLayout']) { editor.ui.registry.addIcon('tpLayout', icon.tpLayout) }
  if (!editor.ui.registry.getAll().icons['exportWord']) { editor.ui.registry.addIcon('exportWord', icon.exportWord) }
  if (!editor.ui.registry.getAll().icons['readAloud']) { editor.ui.registry.addIcon('readAloud', icon.readAloud) }
  for (const key in customButton) {
    if (Object.hasOwnProperty.call(customButton, key)) {
      editor.ui.registry.addButton(key, customButton[key])
      editor.ui.registry.addMenuItem(key, { ...customButton[key], text: customButton[key].tooltip })
    }
  }
  initTpLayout(editor)
}