<!--
 * @FileDescription: 筛选高级搜索的条件行组件
 * @Author: 谢育林
 * @Date: 2022-10-1
 * @LastEditors: 谢育林
 * @LastEditTime: 2022-10-9
 -->
<template>
  <div class="xyl-search-item">
    <el-select v-model="columnId" class="xyl-search-column" @change="columnChange" placeholder="请选择筛选条件">
      <el-option v-for="item in optionData" :key="item.id" :label="item.name" :value="item.id" />
    </el-select>
    <span class="xyl-search-separate">-</span>
    <el-select v-model="queryType" class="xyl-search-type" placeholder="请选择">
      <el-option v-for="item in queryTypeData" :key="item.key" :label="item.name" :value="item.key" />
    </el-select>
    <span class="xyl-search-separate">-</span>
    <template v-if="valType === 'select'">
      <el-select v-model="value" class="xyl-search-option" placeholder="请选择筛选条件" filterable clearable>
        <el-option v-for="item in dictTypeData[dictType]" :key="item.key" :label="item.name" :value="item.key" />
      </el-select>
    </template>
    <template v-if="valType === 'selectTree'">
      <el-tree-select
        v-model="value"
        class="xyl-search-option"
        :data="dictType === 'default' ? defaultData[columnId] : screeningData[columnId]"
        check-strictly
        node-key="id"
        :render-after-expand="false"
        placeholder="请选择筛选条件"
        filterable
        clearable />
    </template>
    <template v-if="valType === 'date'">
      <el-date-picker
        v-model="value"
        type="datetime"
        value-format="x"
        class="xyl-search-option"
        placeholder="请选择高级搜索默认值" />
    </template>
    <template v-if="valType === 'YYYY-MM-DD HH:mm'">
      <el-date-picker
        v-model="value"
        type="datetime"
        value-format="x"
        format="YYYY-MM-DD HH:mm"
        class="xyl-search-option"
        placeholder="请选择高级搜索默认值" />
    </template>
    <template v-if="valType === 'YYYY-MM-DD'">
      <el-date-picker
        v-model="value"
        type="date"
        value-format="x"
        class="xyl-search-option"
        placeholder="请选择高级搜索默认值" />
    </template>
    <template v-if="valType === 'YYYY-MM'">
      <el-date-picker
        v-model="value"
        type="month"
        value-format="x"
        class="xyl-search-option"
        placeholder="请选择高级搜索默认值" />
    </template>
    <template v-if="valType === 'YYYY'">
      <el-date-picker
        v-model="value"
        placeholder="请选择高级搜索默认值"
        value-format="YYYY"
        class="xyl-search-option"
        type="year" />
    </template>
    <template v-if="!valType || valType === 'input'">
      <el-input v-model="value" class="xyl-search-option" placeholder="请输入关键词" clearable />
    </template>
  </div>
</template>
<script>
export default { name: 'XylSearchItem' }
</script>
<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { format } from 'common/js/time.js'
const props = defineProps({
  columnId: { type: String, default: '' },
  queryType: { type: String, default: '' },
  value: { type: String, default: '' },
  optionData: { type: Array, default: () => [] },
  queryTypeData: { type: Array, default: () => [] },
  dictTypeData: { type: Object, default: () => ({}) },
  screeningData: { type: Object, default: () => [] },
  defaultData: { type: Object, default: () => [] }
})
const emit = defineEmits(['update:columnId', 'update:queryType', 'update:value', 'update:valueName'])
// 筛选条件的选择
const columnId = computed({
  get() {
    return props.columnId
  },
  set(val) {
    emit('update:columnId', val)
  }
})
// 筛选类型的选择
const queryType = computed({
  get() {
    return props.queryType
  },
  set(val) {
    emit('update:queryType', val)
  }
})
// 筛选值的选择
const value = computed({
  get() {
    return props.value
  },
  set(val) {
    emit('update:value', val)
  }
})
// 筛选值的选择
const valueName = computed({
  get() {
    return props.value
  },
  set(val) {
    emit('update:valueName', val)
  }
})
// 筛选条件的数据
const optionData = computed(() => props.optionData)
// 筛选类型的数据
const queryTypeData = computed(() => props.queryTypeData)
// 筛选值的数据
const dictTypeData = computed(() => props.dictTypeData)
// 筛选值的数据
const defaultData = computed(() => {
  var obj = {}
  props.defaultData.forEach((item) => {
    obj[item.id] = item.data
  })
  return obj
})
// 筛选值的数据
const screeningData = computed(() => {
  var obj = {}
  props.screeningData.forEach((item) => {
    obj[item.id] = item.data
  })
  return obj
})
// 筛选项的类型
const valType = ref('')
// 字典标识
const dictType = ref('')
onMounted(() => {
  optionChange()
})
watch(
  () => props.columnId,
  () => {
    optionChange()
  }
)
watch(
  () => props.optionData,
  () => {
    optionChange()
  }
)
watch(
  [() => value.value, () => dictTypeData.value, () => defaultData.value, () => screeningData.value],
  () => {
    if (valType.value === 'select') {
      selectData(dictTypeData.value[dictType.value] || [])
    }
    if (valType.value === 'selectTree') {
      selectData(
        dictType.value === 'default' ? defaultData.value[columnId.value] : screeningData.value[columnId.value] || []
      )
    }
    if (valType.value === 'date') {
      valueName.value = format(Number(value.value))
    }
    if (valType.value === 'YYYY-MM-DD HH:mm') {
      valueName.value = format(Number(value.value), valType.value)
    }
    if (valType.value === 'YYYY-MM-DD') {
      valueName.value = format(Number(value.value), valType.value)
    }
    if (valType.value === 'YYYY-MM') {
      valueName.value = format(Number(value.value), valType.value)
    }
    if (valType.value === 'YYYY') {
      valueName.value = value.value
    }
    if (!valType.value || valType.value === 'input') {
      valueName.value = value.value
    }
  },
  { immediate: true }
)
const selectData = (data) => {
  for (let index = 0; index < data?.length || 0; index++) {
    const item = data[index]
    if (item.id === value.value) {
      valueName.value = item.label
    }
    if (item?.children?.length) {
      selectData(item.children)
    }
  }
}
const optionChange = () => {
  optionData.value.forEach((row) => {
    if (row.id === columnId.value) {
      valType.value = row.valType
      dictType.value = row.dictType
    }
  })
}
const columnChange = () => {
  value.value = ''
}
</script>
<style lang="scss">
.xyl-search-item {
  display: flex;
  align-items: center;

  .xyl-search-column {
    width: 180px;
  }

  .xyl-search-separate {
    padding: 0 6px;
  }

  .xyl-search-type {
    width: 120px;
  }

  .xyl-search-option {
    width: 220px;
  }
}
</style>
