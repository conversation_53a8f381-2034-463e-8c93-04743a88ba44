import HTTP, { baseURL } from './index'
import { saveAs } from 'file-saver'
import utils from '../js/utils.js'
const openImgEncrypt = (fileId) =>
  btoa(
    encodeURIComponent(utils.encrypt(fileId, new Date().getTime(), '1')).replace(/%([0-9A-F]{2})/g, (match, p1) => {
      return String.fromCharCode('0x' + p1)
    })
  )
const GlobalApi = {
  fileURL(id) {
    const token = sessionStorage.getItem('token') || ''
    let urlPath = ''
    if (token) {
      urlPath = id + (id.indexOf('?') !== -1 ? '&Authorization=' : '?Authorization=') + token
    } else {
      urlPath = 'SHARE_' + openImgEncrypt(id)
    }
    return `${baseURL}/image/${urlPath}`
  },
  openImgURL(id) {
    return `${baseURL}/image/SHARE_${openImgEncrypt(id)}`
  },
  defaultImgURL(id) {
    return `${baseURL}/img/${id}`
  },
  filePreview(id) {
    return `${baseURL}/file/preview/${id}`
  },
  globalFileInfo(id) {
    return HTTP.json(`/file/info/${id}`)
  },
  globalUpload(params, callback, id) {
    // 上传文件
    return HTTP.fileUpload('/file/upload', params, callback, id)
  },
  globalDownload(fileId, params) {
    // 下载文件
    if (sessionStorage.getItem('token')) {
      return HTTP.fileDownload(`/file/download/${fileId}`, params)
    } else {
      return HTTP.fileDownload(`/in_system/file/download/${fileId}`, params)
    }
  },
  inSystemGlobalDownload(fileId, params) {
    // 公开下载文件
    return HTTP.fileDownload(`/in_system/file/download/${fileId}`, params)
  },
  ofdTopdf(params) {
    // ofd转pdf
    return HTTP.fileUploadDownload('/file/ofd2pdf', params)
  },
  wordTopdf(params) {
    // word转pdf
    if (sessionStorage.getItem('token')) {
      return HTTP.fileUploadDownload('/file/word2pdf', params)
    } else {
      return HTTP.fileUploadDownload('/in_system/file/word2pdf', params)
    }
  },
  xlsToxlsx(params) {
    // xls转xlsx
    if (sessionStorage.getItem('token')) {
      return HTTP.fileUploadDownload('/file/xls2xlsx', params)
    } else {
      return HTTP.fileUploadDownload('/in_system/file/xls2xlsx', params)
    }
  },
  globalDocumentTemplate(id, code, callback) {
    // 通过编号获取文档模板
    return HTTP.fileDownload(`/documentTemplate/load/${code}`, {}, 'arraybuffer', {
      onDownloadProgress: (progressEvent) => {
        callback(progressEvent, id)
      }
    })
  },
  globalDocumentZip(url, params) {
    // 下载解析zip
    return HTTP.fileDownload(url, params, 'arraybuffer')
  },
  globalElectronFileDownload(id, fileId, params, callback) {
    return HTTP.fileDownload(`/file/download/${fileId}`, params, 'arraybuffer', {
      onDownloadProgress: (progressEvent) => {
        callback(progressEvent, id)
      }
    })
  },
  globalFileDownload(fileId, fileName, params) {
    // 下载文件
    if (sessionStorage.getItem('token')) {
      HTTP.fileDownload(`/file/download/${fileId}`, params).then((res) => {
        saveAs(new Blob([res]), fileName)
      })
    } else {
      HTTP.fileDownload(`/in_system/file/download/${fileId}`, params).then((res) => {
        saveAs(new Blob([res]), fileName)
      })
    }
  },
  globalProgressDownload(id, fileId, params, callback) {
    // 下载文件
    if (sessionStorage.getItem('token')) {
      return HTTP.fileDownload(`/file/download/${fileId}`, params, 'blob', {
        onDownloadProgress: (progressEvent) => {
          callback(progressEvent, id)
        }
      })
    } else {
      return HTTP.fileDownload(`/in_system/file/download/${fileId}`, params, 'blob', {
        onDownloadProgress: (progressEvent) => {
          callback(progressEvent, id)
        }
      })
    }
  },
  globalProgressDownloadZip(params, id, callback) {
    // 批量下载文件
    return HTTP.fileDownload('/file/zip', params, 'blob', {
      onDownloadProgress: (progressEvent) => {
        callback(progressEvent, id)
      }
    })
  },
  // 其他接口文件下载
  globalOtherDownload(url, params, id, callback, config = {}) {
    // 下载文件
    return HTTP.fileDownload(url, params, 'blob', {
      ...config,
      onDownloadProgress: (progressEvent) => {
        callback(progressEvent, id)
      }
    })
  },
  fileIdData(params) {
    // 根据文件id获取文件列表
    return HTTP.json('/attachment/list', params)
  },
  selectUserJson(params) {
    // 获取选人配置json
    return HTTP.get('static_config/selectUser.json', params)
  },
  customSelectUser(params) {
    // 获取选人配置json
    return HTTP.get('static_config/customSelectUser.json', params, { noErrorTip: true })
  },
  currentTheme(params) {
    // 当前启用的主题
    return HTTP.json('/pageTheme/currentTheme', params)
  },
  SelectPersonTab(params, config) {
    // 选人控件选项卡
    return HTTP.json('/choose/tabs', params, config)
  },
  SelectPersonGroup(params, config) {
    // 选人控件分组
    return HTTP.json('/choose/tree', params, config)
  },
  SelectPersonUser(params, config) {
    // 选人控件用户
    return HTTP.json('/choose/users', params, config)
  },
  SelectPersonBookUser(params) {
    // 选人控件获取通讯录用户
    return HTTP.json('/relationBookMember/users', params)
  },
  getSelectUser(params) {
    // 选人控件获取用户
    return HTTP.json('/choose/users/exists', params)
  },
  chooseMineTagTree(params) {
    // 选人控件个人自定义分组
    return HTTP.json('/chooseMineTag/tree', params)
  },
  chooseMineTagInfo(params) {
    // 选人控件个人自定义分组
    return HTTP.json('/chooseMineTag/info', params)
  },
  chooseMineTagDel(params) {
    // 选人控件个人自定义分组
    return HTTP.json('/chooseMineTag/dels', params)
  },
  chooseMineTagJoinAllUser(params) {
    // 选人控件个人自定义分组
    return HTTP.json('/chooseMineTag/joinAll/user', params)
  },
  chooseMineTagJoinUser(params) {
    // 选人控件个人自定义分组
    return HTTP.json('/chooseMineTag/join/user', params)
  },
  chooseMineTagCleanUser(params) {
    // 选人控件个人自定义分组
    return HTTP.json('/chooseMineTag/clean/user', params)
  },
  globalGet(url, params) {
    return HTTP.get(url, params)
  },
  globalPost(url, params) {
    return HTTP.post(url, params)
  },
  globalJson(url, params) {
    return HTTP.json(url, params)
  },
  tableHead(id, params) {
    // 获取表头
    return HTTP.get(`/customColumn/list/${id}`, params)
  },
  queryTypeList(params) {
    // 获取表格数据
    return HTTP.json('/customColumn/queryTypes', params)
  },
  customColumnSelector(columnId, params) {
    // 获取通用筛选数据
    return HTTP.json(`/customColumn/selector/${columnId}`, params)
  },
  delColumn(params) {
    // 删除列
    return HTTP.json('/windRunner/delColumn', params)
  },
  windRunnerConfig(params) {
    // 创建特殊字段
    return HTTP.json('/windRunner/config', params)
  },
  windRunnerReadConfig(params) {
    // 读取特殊字段是否创建
    return HTTP.json('/windRunner/readConfig', params)
  },
  globalList(params) {
    // 通用列表查询
    return HTTP.json('/wind/runner/list', params)
  },
  globalInfo(params) {
    // 通用列表详情
    return HTTP.json('/wind/runner/info', params)
  },
  globalDel(params) {
    // 通用列表删除
    return HTTP.json('/wind/runner/dels', params)
  },
  dictionaryData(params) {
    // 获取字典
    return HTTP.json('/dictionary/selector', params)
  },
  openApiDictionaryData(params) {
    // 获取字典
    return HTTP.json('/open_api/dictionary/selector', params)
  },
  dictionaryNameData(params) {
    // 获取字典名称
    return HTTP.json('/dictionaryDefinition/selector', params)
  },
  unitData(params) {
    // 获取机构
    return HTTP.json('/office/selector/tree', params)
  },
  unitSelectorList(params) {
    // 获取选中机构
    return HTTP.json('/office/list', params)
  },
  roleData(params) {
    // 角色选择
    return HTTP.json('/role/selector', params)
  },
  templateInfo(params) {
    // 业务编号获取模板
    return HTTP.json('/themeTemplate/business/info', params)
  },
  globalData(params) {
    // 通用列表查询免登录获取数据
    return HTTP.json('/wind/runner/openList', params)
  },
  globalReadConfig(params) {
    // 通用取配置接口
    return HTTP.json('/config/read', params, { terminal: 'PC' })
  },
  globalReadOpenConfig(params) {
    // 通用公开取配置接口
    return HTTP.json('/config/openRead', params)
  },
  globalExportExcelHead(module) {
    // 通用导出表头
    return HTTP.json(`/excel/export/properties/${module}`)
  },
  globalExportExcelData(module, params) {
    // 通用导出数据
    return HTTP.json(`/excel/export/data/${module}`, params)
  },
  globalExcelImport(url, params, config) {
    // 通用Excel导入数据
    return HTTP.fileUpload(url, params, () => {}, '', config)
  },
  globalExcelHistoryImport(module, params) {
    // 通用下载导入数据模板
    return HTTP.json(`/excel/historyImport/${module}`, params)
  },
  downloadImportResult(code, params) {
    // 下载导入数据结果
    return HTTP.fileDownload(`/excel/downloadImportResult/${code}`, params)
  },
  userExists(params) {
    // 查询是否有手机号一致的用户
    return HTTP.json('/user/exists', params)
  },
  transferFolder(params) {
    // 获取我的文件夹列表
    return HTTP.json('/panfile/menutree', params)
  },
  myNewFolder(params) {
    // 新建我的文件夹
    return HTTP.json('/panfile/addmenu', params)
  },
  myFolderTree(params) {
    // 获取我的文件夹列表
    return HTTP.json('/panfile/menutree', params)
  },
  myNewFolder(params) {
    // 新建我的文件夹
    return HTTP.json('/panfile/addmenu', params)
  },
  transferFile(params) {
    // 个人文件关联附件
    return HTTP.json('/panfile/savefile', params)
  },
  clickRecord(module) {
    // 记录点击数量
    return HTTP.json(`/behavior/record/${module}`)
  },
  clickAcquire(module) {
    // 获取点击数量
    return HTTP.json(`/behavior/count/${module}`)
  },
  twoLevelTree(params) {
    // 评论二层树结构
    return HTTP.json('/comment/twoLevelTree', params)
  },
  twoLevelTreeManage(params) {
    // 评论二层树结构（审核管理）
    return HTTP.json('/comment/twoLevelTreeForCheck', params)
  },
  commentCheckPass(params) {
    // 评论审核通过
    return HTTP.json('/comment/check/pass', params)
  },
  commentCheckNoPass(params) {
    // 评论审核不通过
    return HTTP.json('/comment/check/nopass', params)
  },
  commentNew(params) {
    // 评论新增
    return HTTP.json('/comment/add', params)
  },
  commentEdit(params) {
    // 评论编辑
    return HTTP.json('/comment/edit', params)
  },
  commentInfo(params) {
    // 评论详情
    return HTTP.json('/comment/info', params)
  },
  commentDel(params) {
    // 评论删除
    return HTTP.json('/comment/dels', params)
  },
  businessAreaTree(params) {
    // 查询业务地区树
    return HTTP.json('/area/businessTree', params)
  },
  businessAreaType(params) {
    // 查询业务地区通讯录分类
    return HTTP.json('/relationBookType/list', params)
  },
  businessAreaGroup(params) {
    // 查询业务地区通讯录分组
    return HTTP.json('/relationBook/list', params)
  },
  businessAreaUser(params) {
    // 查询业务地区通讯录用户
    return HTTP.json('/relationBookMember/list', params)
  },
  businessUserAccountList(params) {
    // 查询用户账号id
    return HTTP.json('/userAccount/list', params)
  },
  VisibleRangeType(params) {
    // 可见范围
    return HTTP.json('/authorityRange/selectors', params)
  },
  termYearCurrent(params) {
    // 获取当前届次
    return HTTP.json('/termYear/current', params)
  },
  termYearTree(params) {
    // 获取届次树
    return HTTP.json('/termYear/tree', params)
  },
  termYearSelect(params) {
    // 获取届次下拉
    return HTTP.json('/termYear/select', params)
  },
  unifyStatus(params) {
    // 通用审核
    return HTTP.json('/unify/status', params)
  },
  userGenMobile(params) {
    // 随机手机号
    return HTTP.json('/user/genMobile', params)
  },
  editRecordList(params) {
    // 编辑记录列表
    return HTTP.json('/editRecord/list', params)
  },
  editRecordInfo(params) {
    // 编辑记录详情
    return HTTP.json('/editRecord/info', params)
  },
  dynamicTextEdit(params) {
    // 标题模板编辑
    return HTTP.json('/dynamicText/edit', params)
  },
  dynamicTextInfo(params) {
    // 标题模板详情（占位符）
    return HTTP.json('/dynamicText/info', params)
  },
  dynamicTextByCode(params) {
    // 标题模板详情
    return HTTP.json('/dynamicText/info/byCode', params)
  },
  favoriteFolderList(params) {
    // 收藏文件夹列表
    return HTTP.json('/favoriteFolder/list', params)
  },
  favoriteAdd(params) {
    // 新增收藏
    return HTTP.json('/favorite/add', params)
  },
  authorize(params) {
    return `${baseURL}/oauth/authorize?response_type=code&scope=all&${params}`
  },
  login(params, config = {}) {
    // 登录
    return HTTP.post('/oauth/token', params, { terminal: 'PC', ...config })
  },
  oauthToken(params) {
    // 游客登陆
    return HTTP.post('/oauth/token', params)
  },
  appToken(params) {
    // 登录
    return HTTP.post('/scanCodeLogin/gain/appToken', params, { terminal: 'PC' })
  },
  verifyLoginCode(params) {
    return HTTP.json('/open_api/verifyCode/enableStatus', params)
  },
  licenceInfo(params) {
    return HTTP.get('/licence/info', params)
  },
  licenceUpdate(params) {
    return HTTP.get('/licence/update', params)
  },
  loginUser(params) {
    // 登录获取用户信息
    return HTTP.get('/login/user', params, { terminal: 'PC' })
  },
  verifyLoginUser(params, areaId) {
    // 登录获取用户信息
    return HTTP.get('/login/user', params, { areaId: areaId })
  },
  loginMenu(params) {
    // 登录获取菜单信息
    return HTTP.get('/login/menus', params, { terminal: 'PC' })
  },
  loginRole(params) {
    // 登录获取角色信息
    return HTTP.get('/login/roles', params, { terminal: 'PC' })
  },
  loginAreas(params) {
    // 获取地区
    return HTTP.json('/login/areas', params, { terminal: 'PC' })
  },
  loginOut(params) {
    // 获取地区
    return HTTP.json('/login/out', params, { terminal: 'PC' })
  },
  verifyCodeSend(params) {
    // 验证码发送
    return HTTP.json('/verifyCode/send', params)
  },
  openVerifyCodeSend(params) {
    // 验证码发送
    return HTTP.json('/open_api/verifyCode/send', params)
  },
  findPassword(params) {
    // 忘记密码
    return HTTP.json('/open_api/userAccount/findPassword', params)
  },
  passwordStrengthChecker(params) {
    // 验证密码
    return HTTP.json('/passwordStrengthChecker', params)
  },
  passwordStrengthMessage(params) {
    // 获取密码校验的文字
    return HTTP.json('/passwordStrengthMessage', params)
  },
  tencentVideo(params) {
    // 获取腾讯云签名
    return HTTP.json('/sign/tencentVideo', params)
  },
  fileword2html(params) {
    // word转html
    return HTTP.fileUpload('/file/word2html', params, () => {})
  },
  AISceneDetail(params) {
    // AI智能场景管理
    return HTTP.json('/aigptChatScene/infobycode', params)
  },
  rongCloud(url, params, type) {
    // 融云接口
    if (type) {
      return HTTP.json(`${url}push/rongCloud/${params.type}`, params)
    } else {
      return HTTP.post(`${url}push/rongCloud`, params)
    }
  },
  // 大数据服务接口
  typingVerification(params) {
    // 错别字分析
    return HTTP.json('/summoner/open/typingVerification', params)
  },
  wrongWord(params) {
    // 错别字分析
    return HTTP.json('/hadoop_api/datax/pubApi/corrector2', params)
  },
  inSystemWordTopdf(params) {
    // 公开 word转pdf
    return HTTP.fileUploadDownload('/in_system/file/word2pdf', params)
  },
  // wordToPdf (params) {
  //   // 下载文件
  //   return HTTP.fileDownload('/hadoop_api/datax/wordApi/wordToPdf', params)
  // },
  homePageDocument(params) {
    // 通过编号获取首页数据
    return HTTP.json('/homepage/load', params)
  },
  longShortLink(url, params) {
    // 长链接转短链接
    if (sessionStorage.getItem('token')) {
      return HTTP.get(`/longShortLink/exchange?longUrl=${url}`, params)
    } else {
      return HTTP.get(`/open_api/longShortLink/exchange?longUrl=${url}`, params)
    }
  },
  file_preview(params) {
    return HTTP.post('https://www.yozodcs.com/fcscloud/file/http', params, { noVerify: true })
  },
  file_preview_url(params) {
    return HTTP.post('https://www.yozodcs.com/fcscloud/composite/convert', params, { noVerify: true })
  },
  genMeetingToken(params) {
    // 亿联获取accessToken
    return HTTP.json('/stationMeeting/genMeetingToken', params)
  }
}
export default GlobalApi
